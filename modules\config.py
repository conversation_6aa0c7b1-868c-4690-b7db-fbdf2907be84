"""
配置管理模块
管理项目的全局配置、常量和状态
"""

import os
import threading
from typing import Dict, List, Any

# 全局锁，用于保护共享资源（与原版本完全一致）
lock = threading.Lock()

class Config:
    """配置管理类"""
    
    # 目录配置
    SCREENSHOTS_DIR = "screenshots"
    TEMPLATES_DIR = "templates"
    STATIC_DIR = "static"
    STATIC_IMG_DIR = os.path.join(STATIC_DIR, "img")
    
    # 调试配置
    DEBUG_MODE = True
    
    # Flask配置
    FLASK_SECRET_KEY = 'wind_power_safety_app_key'
    FLASK_HOST = '127.0.0.1'
    FLASK_PORT = 5000
    FLASK_DEBUG = False  # 关闭调试模式避免重复输出
    
    # 浏览器配置
    EDGE_DRIVER_PATH = os.path.join("edgedriver_win64", "msedgedriver.exe")

    # 截图配置
    DEFAULT_SCREENSHOT_INTERVAL = 30  # 默认截图间隔（秒）
    MIN_SCREENSHOT_INTERVAL = 5       # 最小截图间隔（秒）
    MAX_SCREENSHOT_INTERVAL = 300     # 最大截图间隔（秒）

    # 页面刷新配置
    DEFAULT_REFRESH_INTERVAL = 30      # 默认页面刷新间隔（秒）
    MIN_REFRESH_INTERVAL = 1          # 最小页面刷新间隔（秒）
    MAX_REFRESH_INTERVAL = 300         # 最大页面刷新间隔（秒）
    
    # 登录URL
    LOGIN_URL = ("https://id.ceic.com/auth/realms/sh4a/protocol/openid-connect/auth?"
                "client_id=JTajhxt&redirect_uri=http%3A%2F%2Fsafety.ceic.com&"
                "response_type=code&scope=openid&state=1918223715797377024")
    
    # 目标页面URL
    TARGET_URL = "http://safety.ceic.com/machinery/20/40"
    
    # 监控配置
    CAMERA_LOAD_WAIT = 5     # 摄像头加载等待时间（秒）
    
    # 图像分析配置
    BLACK_THRESHOLD = 30     # 黑屏检测阈值
    MIN_CONTOUR_AREA = 100   # 最小轮廓面积
    
    @classmethod
    def ensure_directories(cls):
        """确保所有必要的目录存在"""
        # 使用当前工作目录（应该已经在main_new.py中设置为项目根目录）
        current_dir = os.getcwd()

        directories = [
            os.path.join(current_dir, cls.SCREENSHOTS_DIR),
            os.path.join(current_dir, cls.TEMPLATES_DIR),
            os.path.join(current_dir, cls.STATIC_DIR),
            os.path.join(current_dir, cls.STATIC_IMG_DIR)
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            print(f"确保目录存在: {directory}")
            
    @classmethod
    def get_driver_path(cls):
        """获取驱动路径"""
        driver_path = os.path.abspath(cls.EDGE_DRIVER_PATH)
        print(f"使用的驱动路径: {driver_path}")
        return driver_path


class GlobalState:
    """全局状态管理类"""
    
    def __init__(self):
        # 不再需要实例锁，使用全局锁

        # 浏览器状态
        self.global_driver = None
        self.monitoring_active = False
        self.monitoring_threads = []

        # 数据存储
        self.job_info_list = []
        self.abnormal_images = []
        self.is_secondary_company = False
        self.login_credentials = None
        self.start_monitoring_requested = False

        # 截图配置
        self.screenshot_interval = Config.DEFAULT_SCREENSHOT_INTERVAL

        # 页面刷新配置
        self.refresh_interval = Config.DEFAULT_REFRESH_INTERVAL

        # 任务状态
        self.task_status = {
            "is_complete": False,
            "has_abnormal": False,
            "result": ""
        }
    
    def reset_task_status(self):
        """重置任务状态"""
        self.task_status = {
            "is_complete": False,
            "has_abnormal": False,
            "result": ""
        }
        self.job_info_list = []
        self.abnormal_images = []
    
    def add_image(self, image_info: Dict[str, Any]):
        """添加图片信息（包含所有图片）"""
        self.abnormal_images.append(image_info)

        # 如果有黑屏或危险行为，标记为有异常
        if image_info.get("is_black_screen", False) or image_info.get("has_danger", False):
            self.task_status["has_abnormal"] = True

    def add_abnormal_image(self, image_info: Dict[str, Any]):
        """兼容性方法，调用add_image"""
        self.add_image(image_info)

    def set_screenshot_interval(self, interval: int) -> bool:
        """设置截图间隔"""
        if Config.MIN_SCREENSHOT_INTERVAL <= interval <= Config.MAX_SCREENSHOT_INTERVAL:
            self.screenshot_interval = interval
            print(f"📸 截图间隔已设置为: {interval}秒")
            return True
        else:
            print(f"❌ 截图间隔必须在{Config.MIN_SCREENSHOT_INTERVAL}-{Config.MAX_SCREENSHOT_INTERVAL}秒之间")
            return False

    def get_screenshot_interval(self) -> int:
        """获取当前截图间隔"""
        return self.screenshot_interval

    def set_refresh_interval(self, interval: int) -> bool:
        """设置页面刷新间隔"""
        if Config.MIN_REFRESH_INTERVAL <= interval <= Config.MAX_REFRESH_INTERVAL:
            self.refresh_interval = interval
            print(f"🔄 页面刷新间隔已设置为: {interval}秒")
            return True
        else:
            print(f"❌ 页面刷新间隔必须在{Config.MIN_REFRESH_INTERVAL}-{Config.MAX_REFRESH_INTERVAL}秒之间")
            return False

    def get_refresh_interval(self) -> int:
        """获取当前页面刷新间隔"""
        return self.refresh_interval
    
    def get_latest_abnormal_images(self, count: int = 5) -> List[Dict[str, Any]]:
        """获取最新的异常图片信息"""
        sorted_images = sorted(
            self.abnormal_images,
            key=lambda x: x['timestamp'],
            reverse=True
        )
        return sorted_images[:count]
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False

    def is_monitoring_active(self) -> bool:
        """检查监控是否活跃"""
        return self.monitoring_active


# 全局状态实例
global_state = GlobalState()
